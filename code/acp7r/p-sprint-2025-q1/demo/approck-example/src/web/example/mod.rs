pub mod authbasic;
pub mod authbasicoption;
pub mod boxit;
pub mod gtypes;
pub mod index;
pub mod name;
pub mod pathcap1;
pub mod post1;
pub mod post2;
pub mod proxy;
pub mod querystring1;
pub mod querystring2;
pub mod querystring3;
pub mod querystring4;
pub mod redis1;
pub mod stream;
pub mod userlist1;
pub mod video;
pub mod websocket1;

#[approck::prefix(/example/)]
pub mod prefix {
    pub fn menu(menu: Menu) {
        menu.set_label_uri("Examples", "/example/");

        // Basic Examples
        menu.add_link("All Examples", "/example/");
        menu.add_link("Path Capture", "/example/pathcap1");
        menu.add_link("Forms (Name)", "/example/name");
        menu.add_link("POST Form 1", "/example/post1");
        menu.add_link("POST Form 2", "/example/post2");

        // Query String Examples
        menu.add_link("Query String 1", "/example/querystring1");
        menu.add_link("Query String 2", "/example/querystring2");
        menu.add_link("Query String 3", "/example/querystring3");
        menu.add_link("Query String 4", "/example/querystring4");

        // Authentication Examples
        menu.add_link("Basic Auth", "/example/authbasic");
        menu.add_link("Basic Auth Option", "/example/authbasicoption");

        // Advanced Examples
        menu.add_link("Redis Demo", "/example/redis1");
        menu.add_link("User List", "/example/userlist1");
        menu.add_link("WebSocket Demo", "/example/websocket1");
        menu.add_link("Canvas Demo", "/example/boxit/");
        menu.add_link("Proxy Example", "/example/proxy");
        menu.add_link("Stream Response", "/example/stream");
        menu.add_link("Video Example", "/example/video");

        // GTypes
        menu.add_link("GTypes Example 18", "/example/gtypes/example18");
    }
}
