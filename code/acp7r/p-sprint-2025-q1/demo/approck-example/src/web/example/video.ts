import { SEC } from "@granite/lib.mts";

// Module-level variables
const $video: HTMLVideoElement = SEC(HTMLVideoElement, document, '#video');
const $canvas: HTMLCanvasElement = SEC(HTMLCanvasElement, document, '#canvas');
const $start_button: HTMLButtonElement = SEC(HTMLButtonElement, document, '#start-camera');
const $stop_button: HTMLButtonElement = SEC(HTMLButtonElement, document, '#stop-camera');
const $status_element: HTMLElement = SEC(HTMLElement, document, '#status');

const canvas_ctx = $canvas.getContext('2d');
if (!canvas_ctx) {
    throw new Error('Could not get canvas context');
}
const ctx: CanvasRenderingContext2D = canvas_ctx;

let stream: MediaStream | null = null;
let animation_id: number | null = null;

// Frame rate tracking
let total_frame_count: number = 0;
let fps_frame_count: number = 0;
let last_frame_time: number = 0;
let fps: number = 0;
const fps_update_interval: number = 1000; // Update FPS every second

// Data flow tracking
let bytes_processed: number = 0;
let start_time: number = 0;

// Setup event listeners
$start_button.addEventListener("click", () => start_camera());
$stop_button.addEventListener("click", () => stop_camera());

async function start_camera(): Promise<void> {
    try {
        update_status("Requesting camera access...");

        // Request camera access - HD quality
        stream = await navigator.mediaDevices.getUserMedia({
            video: {
                width: { ideal: 1280 },
                height: { ideal: 720 },
                frameRate: { ideal: 30 },
            },
            audio: false,
        });

        // Set the video source to the camera stream
        $video.srcObject = stream;

        // Wait for video to be ready and start playing
        await new Promise<void>((resolve) => {
            $video.onloadedmetadata = () => {
                // Resize canvas to match actual video dimensions
                const width = $video.videoWidth || 1280;
                const height = $video.videoHeight || 720;
                console.log(`Video dimensions: ${width}x${height}`);
                $canvas.width = width;
                $canvas.height = height;

                $video.play().then(() => {
                    resolve();
                }).catch((error) => {
                    console.error("Error playing video:", error);
                    resolve(); // Continue anyway
                });
            };
        });

        // Initialize tracking
        total_frame_count = 0;
        fps_frame_count = 0;
        last_frame_time = performance.now();
        start_time = performance.now();
        bytes_processed = 0;
        fps = 0;

        // Give a small delay to ensure video is actually playing
        setTimeout(() => {
            start_drawing();
        }, 100);

        // Update UI
        $start_button.disabled = true;
        $stop_button.disabled = false;
        update_status("Camera active - video streaming to canvas");
    } catch (error) {
        console.error("Error accessing camera:", error);
        update_status(
            `Error: ${error instanceof Error ? error.message : "Failed to access camera"}`,
        );
    }
}

function stop_camera(): void {
    // Stop the video stream
    if (stream) {
        stream.getTracks().forEach((track) => track.stop());
        stream = null;
    }

    // Stop animation
    if (animation_id) {
        cancelAnimationFrame(animation_id);
        animation_id = null;
    }

    // Clear canvas
    ctx.clearRect(0, 0, $canvas.width, $canvas.height);

    // Update UI
    $start_button.disabled = false;
    $stop_button.disabled = true;
    update_status("Camera stopped");
}

function start_drawing(): void {
    const draw = () => {
        const current_time = performance.now();

        if ($video.readyState >= $video.HAVE_CURRENT_DATA && !$video.paused) {
            // Clear canvas first
            ctx.clearRect(0, 0, $canvas.width, $canvas.height);

            // Draw the video frame to the canvas
            ctx.drawImage($video, 0, 0, $canvas.width, $canvas.height);

            // Update frame tracking
            total_frame_count++;
            fps_frame_count++;

            // Estimate bytes processed (rough calculation based on canvas size and frame rate)
            const bytes_per_frame = $canvas.width * $canvas.height * 4; // RGBA
            bytes_processed += bytes_per_frame;

            // Calculate FPS every second
            if (current_time - last_frame_time >= fps_update_interval) {
                fps = Math.round((fps_frame_count * 1000) / (current_time - last_frame_time));
                fps_frame_count = 0;
                last_frame_time = current_time;
            }

            // Draw overlay with statistics
            draw_overlay(current_time);
        } else {
            // Draw a placeholder if video isn't ready
            ctx.fillStyle = "#333";
            ctx.fillRect(0, 0, $canvas.width, $canvas.height);
            ctx.fillStyle = "white";
            ctx.font = "14px Arial";
            ctx.textAlign = "center";
            ctx.fillText("Loading camera...", $canvas.width / 2, $canvas.height / 2);
            ctx.textAlign = "left"; // Reset alignment
        }

        // Continue animation if stream is active
        if (stream) {
            animation_id = requestAnimationFrame(draw);
        }
    };

    draw();
}

function draw_overlay(current_time: number): void {
    // Calculate runtime
    const runtime_seconds = Math.floor((current_time - start_time) / 1000);
    const minutes = Math.floor(runtime_seconds / 60);
    const seconds = runtime_seconds % 60;

    // Calculate data rate (MB/s)
    const total_mb = bytes_processed / (1024 * 1024);
    const data_rate_mbps = total_mb / (runtime_seconds || 1);

    // Get video track info if available
    let resolution = `${$canvas.width}x${$canvas.height}`;
    if (stream) {
        const video_track = stream.getVideoTracks()[0];
        if (video_track && video_track.getSettings) {
            const settings = video_track.getSettings();
            if (settings.width && settings.height) {
                resolution = `${settings.width}x${settings.height}`;
            }
        }
    }

    // Prepare overlay text
    const overlay_lines = [
        "Live Camera Feed",
        `FPS: ${fps}`,
        `Resolution: ${resolution}`,
        `Runtime: ${minutes}:${seconds.toString().padStart(2, "0")}`,
        `Data: ${total_mb.toFixed(1)} MB`,
        `Rate: ${data_rate_mbps.toFixed(2)} MB/s`,
        `Frames: ${total_frame_count}`,
    ];

    // Draw semi-transparent background
    const line_height = 16;
    const padding = 8;
    const overlay_width = 180;
    const overlay_height = overlay_lines.length * line_height + padding * 2;

    ctx.fillStyle = "rgba(0, 0, 0, 0.7)";
    ctx.fillRect(5, 5, overlay_width, overlay_height);

    // Draw text
    ctx.font = "12px monospace";
    ctx.fillStyle = "#00ff00"; // Green text for tech look

    overlay_lines.forEach((line, index) => {
        ctx.fillText(line, 10, 20 + index * line_height);
    });
}

function update_status(message: string): void {
    $status_element.textContent = message;
}
