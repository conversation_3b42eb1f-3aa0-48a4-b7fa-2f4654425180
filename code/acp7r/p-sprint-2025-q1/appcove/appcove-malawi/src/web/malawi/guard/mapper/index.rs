#[approck::http(GET /malawi/guard/{guard_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use chrono::Local;

        use crate::web::malawi::guard::mapper::index::guard_detail;
        use maud::html;

        let guard = guard_detail::call(
            app,
            identity,
            guard_detail::Input {
                guard_uuid: path.guard_uuid,
            },
        )
        .await?;

        doc.page_nav_edit_record("Edit guard", &crate::ml_guard_edit(guard.guard_uuid));
        doc.page_nav_delete_record("Delete guard", &crate::ml_guard_delete(guard.guard_uuid));

        doc.set_title("Guard Details");

        let mut table = bux::component::info_table(&guard);
        table.set_heading("Guard Information");
        table.add_name_row(|u| &u.first_name);
        table.add_name_row(|u| &u.last_name);
        table.add_create_ts(|u| u.create_ts.with_timezone(&Local));
        table.add_note_row(|u| u.note.as_deref().unwrap_or(""));
        table.add_active_status_row("Status:", |u| u.active);

        doc.add_body(html!((table)));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
mod guard_detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_uuid: Uuid,
        pub create_ts: DateTimeUtc,
        pub first_name: String,
        pub last_name: String,
        pub phone: Option<String>,
        pub note: Option<String>,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.guard_read(input.guard_uuid) {
            return_authorization_error!("insufficient permissions to guard {}", input.guard_uuid);
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $guard_uuid: &input.guard_uuid,
            };
            row = {
                guard_uuid: Uuid,
                create_ts: DateTimeUtc,
                first_name: String,
                last_name: String,
                phone: Option<String>,
                note: Option<String>,
                active: bool,
            };
            SELECT
                guard_uuid,
                create_ts,
                first_name,
                last_name,
                phone,
                note,
                active
            FROM
                appcove_malawi.guard
            WHERE true
                AND guard_uuid = $guard_uuid::uuid
        )
        .await?;

        Ok(Output {
            guard_uuid: row.guard_uuid,
            create_ts: row.create_ts,
            first_name: row.first_name,
            last_name: row.last_name,
            phone: row.phone,
            note: row.note,
            active: row.active,
        })
    }
}
