#[approck::http(GET /malawi/attendance/{guard_attendance_uuid:Uuid}/; AUTH None; return HTML;)]
pub mod page {
    use chrono::Local;

    pub async fn request(
        app: App,
        identity: Identity,
        doc: Document,
        path: Path,
    ) -> Result<Response> {
        use crate::web::malawi::attendance::mapper::index::guard_attendance_detail;
        use maud::html;

        let guard_attendance = guard_attendance_detail::call(
            app,
            identity,
            guard_attendance_detail::Input {
                guard_attendance_uuid: path.guard_attendance_uuid,
            },
        )
        .await?;

        doc.page_nav_edit_record(
            "Edit attendance",
            &crate::ml_attendance_edit(guard_attendance.guard_attendance_uuid),
        );

        doc.page_nav_delete_record(
            "Delete attendance",
            &crate::ml_attendance_delete(guard_attendance.guard_attendance_uuid),
        );

        doc.set_title("Guard Attendance Details");

        let mut table = bux::component::info_table(guard_attendance);
        table.set_heading("Guard Attendance Information");
        table.add_row("Guard:", |u| html! { (u.guard_name) });
        table.add_row(
            "Start Time:",
            |u| html! { (u.start_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M")) },
        );
        table.add_row(
            "End Time:",
            |u| html! { (u.end_ts.with_timezone(&Local).format("%Y-%m-%d %H:%M")) },
        );
        table.add_note_row(|u| u.note.as_deref().unwrap_or(""));
        table.add_active_status_row("Status:", |u| u.active);

        doc.add_body(html!((table)));

        Ok(Response::HTML(doc.into()))
    }
}

#[approck::api]
pub mod guard_attendance_detail {
    use granite::return_authorization_error;

    #[granite::gtype(ApiInput)]
    pub struct Input {
        pub guard_attendance_uuid: Uuid,
    }

    #[granite::gtype(ApiOutput)]
    pub struct Output {
        pub guard_attendance_uuid: Uuid,
        pub guard_uuid: Uuid,
        pub guard_name: String,
        pub create_ts: DateTimeUtc,
        pub start_ts: DateTimeUtc,
        pub end_ts: DateTimeUtc,
        pub note: Option<String>,
        pub active: bool,
    }

    pub async fn call(app: App, identity: Identity, input: Input) -> Result<Output> {
        if !identity.attendance_read(input.guard_attendance_uuid) {
            return_authorization_error!("insufficient permissions to attendance read");
        }

        let dbcx = app.postgres_dbcx().await?;

        let row = granite::pg_row!(
            db = dbcx;
            args = {
                $guard_attendance_uuid: &input.guard_attendance_uuid,
            };
            row = {
                guard_attendance_uuid: Uuid,
                guard_uuid: Uuid,
                guard_name: String,
                create_ts: DateTimeUtc,
                start_ts: DateTimeUtc,
                end_ts: DateTimeUtc,
                note: Option<String>,
                active: bool,
            };
            SELECT
                s.guard_attendance_uuid,
                s.guard_uuid,
                g.first_name || " " || g.last_name AS guard_name,
                s.create_ts,
                s.start_ts,
                s.end_ts,
                s.note,
                s.active
            FROM
                appcove_malawi.guard_attendance s
                INNER JOIN appcove_malawi.guard g ON g.guard_uuid = s.guard_uuid
            WHERE true
                AND s.guard_attendance_uuid = $guard_attendance_uuid::uuid
        )
        .await?;

        Ok(Output {
            guard_attendance_uuid: row.guard_attendance_uuid,
            guard_uuid: row.guard_uuid,
            guard_name: row.guard_name,
            create_ts: row.create_ts,
            start_ts: row.start_ts,
            end_ts: row.end_ts,
            note: row.note,
            active: row.active,
        })
    }
}
